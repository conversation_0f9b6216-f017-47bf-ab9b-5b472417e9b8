const utils = require('./utils/util');
const user = require('./request/user')

App({
    onLaunch() {
        //检查登陆状态
        this.checkGlobalLoginStatus();
    },

    // 全局登录状态检查
    checkGlobalLoginStatus() {
        console.log('App启动 - 开始检查全局登录状态');

        // 检查微信登录session是否过期
        wx.checkSession({
            success: () => {
                console.log('微信session有效');
                // session有效，检查本地存储的用户信息
                this.checkLocalUserInfo();
            },
            fail: () => {
                console.log('微信session已过期，清理本地数据');
                // session过期，清理相关存储
                this.clearExpiredData();
            }
        });
    },

    // 检查本地用户信息
    checkLocalUserInfo() {
        try {
            const userInfo = wx.getStorageSync('userInfo');
            const openId = wx.getStorageSync('OpenId');

            console.log('本地用户信息检查:', {
                hasUserInfo: !!userInfo,
                hasOpenId: !!openId,
                userInfo: userInfo
            });

            if (userInfo && (userInfo.PersonId || userInfo.userId)) {
                console.log('发现有效的本地用户信息，登录状态有效');
                // 用户信息完整，设置全局登录状态
                this.globalData.isLoggedIn = true;
                this.globalData.userInfo = userInfo;
            } else if (openId) {
                console.log('有OpenId但无完整用户信息，需要重新获取用户信息');
                // 有openId但用户信息不完整，可能需要重新登录
                this.globalData.isLoggedIn = false;
            } else {
                console.log('无有效登录信息');
                this.globalData.isLoggedIn = false;
            }
        } catch (error) {
            console.error('检查本地用户信息失败:', error);
            this.globalData.isLoggedIn = false;
        }
    },

    // 清理过期数据
    clearExpiredData() {
        try {
            wx.removeStorageSync('OpenId');
            // 注意：这里不清理userInfo，因为可能在其他地方还需要
            console.log('已清理过期的OpenId');
        } catch (error) {
            console.error('清理过期数据失败:', error);
        }
        this.globalData.isLoggedIn = false;
    },

    globalData: {
        // 现场信息反馈功能的基础URL
        feedbackBaseUrl: 'https://your-feedback-api-domain.com',
        // 全局登录状态
        isLoggedIn: false,
        userInfo: null
    },

    /**
     * 现场信息反馈功能的网络请求方法
     * 用于fbindex页面及相关功能的API调用
     */
    request(options) {
        return new Promise((resolve, reject) => {
            // 从车辆维保小程序的存储中获取用户信息
            const userInfo = wx.getStorageSync("userInfo");
            const currentCompany = wx.getStorageSync("currentCompany");

            // 默认请求配置
            const defaultOptions = {
                method: 'GET',
                timeout: 10000,
                header: {
                    "Content-Type": "application/json",
                },
            };

            // 如果有用户信息，添加到header中
            if (userInfo) {
                defaultOptions.header["X-User-Info"] = JSON.stringify({
                    PersonId: userInfo.PersonId || userInfo.userId,
                    PersonName: userInfo.PersonName,
                    Phone: userInfo.Phone
                });
            }

            // 如果有当前公司信息，添加到header中
            if (currentCompany) {
                try {
                    const companyJson = JSON.stringify(currentCompany);
                    defaultOptions.header["X-Current-Company"] = encodeURIComponent(companyJson);
                } catch (error) {
                    console.warn('序列化公司信息失败:', error);
                }
            }

            // 合并默认配置和传入的配置
            const requestOptions = {
                ...defaultOptions,
                ...options,
                header: {
                    ...defaultOptions.header,
                    ...options.header,
                },
                // 处理URL：如果是完整URL则直接使用，否则拼接baseUrl
                url: options.url.startsWith("http")
                    ? options.url
                    : `${this.globalData.feedbackBaseUrl}${options.url}`,
                success: (res) => {
                    resolve(res);
                },
                fail: (error) => {
                    console.error('请求失败:', error);
                    reject(error);
                },
            };

            wx.request(requestOptions);
        });
    }
})
