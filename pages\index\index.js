// index.js
// 获取应用实例
const utils = require('../../utils/util');
const app = getApp()
const { userInfo, OpenId } = require('../../config/common')
const user = require('../../request/user')

Page({
    data: {
        userInfo: {},
    },
    radioChange(e) {
        let items = this.data.userInfo.Company;
        items.forEach(items => {
            if (items.CompanyId == e.detail.value) {
                items.IsMain = true;
            }
            else {
                items.IsMain = false;
            }
        })
        this.setData({
            'userInfo.Company': items
        })
        utils.setStorage(userInfo, this.data.userInfo)
    },
    AgainLogin() {
        wx.removeStorage({
            key: userInfo
        })
        this.getStologin();
    },

    // 跳转到车辆维保模块
    goToVehicleMaintenance() {
        wx.navigateTo({
            url: '../order/order'
        });
    },

    // 跳转到现场信息反馈模块
    goToFeedback() {
        // 检查是否已登录
        console.log("跳转到现场信息反馈 - 当前用户信息:", this.data.userInfo);

        const hasValidUserId = this.data.userInfo && (this.data.userInfo.PersonId || this.data.userInfo.userId);

        if (!hasValidUserId) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            });
            return;
        }

        // 确保用户信息已存储到本地存储中
        try {
            wx.setStorageSync('userInfo', this.data.userInfo);
            console.log("用户信息已同步到本地存储");
        } catch (error) {
            console.error("存储用户信息失败:", error);
        }

        wx.navigateTo({
            url: '../fbindex/fbindex'
        });
    },
    //将登陆信息绑定到页面
    setUserInfo(res) {
        console.log("设置用户信息:", res);
        this.setData({
            userInfo: res
        });

        // 同步到本地存储
        try {
            wx.setStorageSync('userInfo', res);
            console.log("用户信息已同步到本地存储");
        } catch (error) {
            console.error("存储用户信息失败:", error);
        }
    },
    checkSession() {
        //先检查登陆是否过期
        wx.checkSession({
            success: () => {
                /*没有过期的情况获取OpenId数据*/
                utils.getStorage('OpenId').then(
                    res => {
                         this.getStologin();
                    }
                ).catch(err => {
                    this.userLogin();
                })
            },
            fail: () => {
                wx.removeStorage({
                    key: 'OpenId'
                  })
                wx.removeStorage({
                  key: userInfo
                })
                /*过期了直接重新登陆*/
                this.userLogin();
            }
        })
    },
    //code换openid
    userLogin() {
        utils.wxLogin().then(
            res => {
                user.login(res);
            }
        )
    },

    // 调试用户信息
    debugUserInfo() {
        const storageUserInfo = wx.getStorageSync("userInfo");
        const pageUserInfo = this.data.userInfo;

        console.log("调试 - 存储的用户信息:", storageUserInfo);
        console.log("调试 - 页面数据中的用户信息:", pageUserInfo);

        // 如果存储中没有用户信息但页面中有，则同步到存储
        if (!storageUserInfo && pageUserInfo) {
            try {
                wx.setStorageSync('userInfo', pageUserInfo);
                console.log("已将页面用户信息同步到存储");
            } catch (error) {
                console.error("同步用户信息失败:", error);
            }
        }

        const displayInfo = pageUserInfo || storageUserInfo;

        wx.showModal({
            title: '用户信息调试',
            content: `PersonId: ${displayInfo?.PersonId || '无'}\nuserId: ${displayInfo?.userId || '无'}\nPersonName: ${displayInfo?.PersonName || '无'}\nPhone: ${displayInfo?.Phone || '无'}\n存储状态: ${storageUserInfo ? '已存储' : '未存储'}`,
            showCancel: false
        });
    },
    getStologin() {
        utils.getStorage(userInfo).then(res => {

            this.setUserInfo(res);

        }).catch(err => {
            utils.getStorage('OpenId').then(res => {
                //有openid就跳转到登陆页
                wx.redirectTo({
                    url: '../Login/Login',
                })
            }).catch(err => {
                //无openId就重新获取openId
                console.log("没有openID");
            })
        })
    },
    onLoad() {
      
         this.checkSession()
        // this.getStologin();        
    }
})
